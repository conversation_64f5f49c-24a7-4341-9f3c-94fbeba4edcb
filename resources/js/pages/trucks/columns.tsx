import { ColumnDef } from '@/types/table';
import { MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { router } from '@inertiajs/react';
import { Truck } from '@/types/truck';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

export const columns: ColumnDef<Truck>[] = [
  {
    id: 'unit_number',
    accessorKey: 'unit_number',
    header: 'Unit Number',
    enableHiding: false,
    enableSorting: true,
  },
  {
    id: 'make',
    accessorKey: 'make',
    header: 'Make',
    meta: { initiallyVisible: true },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'model',
    accessorKey: 'model',
    header: 'Model',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'year',
    accessorKey: 'year',
    header: 'Year',
    meta: { initiallyVisible: true },
    enableSorting: true,
    filterFn: (row, id, value) => {
      return value.includes(String(row.getValue(id)));
    },
  },
  {
    id: 'status',
    accessorKey: 'status',
    header: 'Truck Status',
    meta: { initiallyVisible: true },
    enableSorting: true,
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return status ? <Badge>{status}</Badge> : '-';
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'fleet_status',
    accessorKey: 'fleet_status',
    header: 'Fleet Status',
    meta: { initiallyVisible: true },
    enableSorting: true,
    cell: ({ row }) => {
      const status = row.getValue('fleet_status') as string;
      return status ? <Badge>{status}</Badge> : '-';
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'plate',
    accessorKey: 'plate',
    header: 'License Plate',
    meta: { initiallyVisible: true },
    enableSorting: true,
  },
  {
    id: 'vin',
    accessorKey: 'vin',
    header: 'VIN',
    meta: { initiallyVisible: false },
  },
  {
    id: 'regulatory_compliance_expiry',
    accessorKey: 'regulatory_compliance_expiry',
    header: 'DOT Expiry',
    meta: { initiallyVisible: true },
    enableSorting: true,
    cell: ({ row }) => {
      const date = row.getValue('regulatory_compliance_expiry') as string;
      return date ? format(new Date(date), 'MM/dd/yyyy') : '-';
    },
  },
  {
    id: 'owner_company',
    accessorKey: 'owner_company',
    header: 'Owner Company',
    meta: { initiallyVisible: false },
  },
  {
    id: 'activation_date',
    accessorKey: 'activation_date',
    header: 'Activation Date',
    meta: { initiallyVisible: false },
    cell: ({ row }) => {
      const date = row.getValue('activation_date') as string;
      return date ? format(new Date(date), 'MM/dd/yyyy') : '-';
    },
  },
  {
    id: 'lease_termination_date',
    accessorKey: 'lease_termination_date',
    header: 'Lease End Date',
    meta: { initiallyVisible: false },
    cell: ({ row }) => {
      const date = row.getValue('lease_termination_date') as string;
      return date ? format(new Date(date), 'MM/dd/yyyy') : '-';
    },
  },
  {
    id: 'equipment',
    header: 'Equipment',
    meta: { initiallyVisible: false },
    cell: ({ row }) => {
      const truck = row.original;
      const equipment = [];
      if (truck.deer_guards) equipment.push('Deer Guards');
      if (truck.dash_camera) equipment.push('Dash Camera');
      if (truck.has_headrack) equipment.push('Headrack');
      return equipment.length > 0 ? equipment.join(', ') : '-';
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    enableHiding: false,
    cell: ({ row }) => {
      const truck = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => router.visit(route('trucks.edit', { truck: truck.id }))}>
              Edit Truck
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                if (confirm('Are you sure you want to delete this truck?')) {
                  router.delete(route('trucks.destroy', { truck: truck.id }));
                }
              }}
            >
              Delete Truck
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
