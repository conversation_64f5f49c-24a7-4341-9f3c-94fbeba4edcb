import { BreadcrumbItem } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { BellIcon, PlusIcon, UploadIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/datatable';
import { columns } from './columns';
import { TruckPageProps } from '@/types/truck';

const breadcrumbs: BreadcrumbItem[] = [
  {
    title: 'Trucks',
    href: '/trucks',
  },
];

export default function Trucks() {
  const { trucks, filters } = usePage<TruckPageProps>().props;

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Trucks" />
      <div className="mt-4 flex items-center justify-end gap-2 px-4 lg:px-4">
        <Button asChild>
          <Link href={route('trucks.create')} className="flex items-center">
            <PlusIcon className="mr-2 h-4 w-4" />
            Add
          </Link>
        </Button>
        <Button asChild>
          <Link href="#" className="flex items-center">
            <BellIcon className="mr-2 h-4 w-4" />
            Notifications
          </Link>
        </Button>
        <Button asChild>
          <Link href={route('trucks.import.index')} className="flex items-center">
            <UploadIcon className="mr-2 h-4 w-4" />
            Import
          </Link>
        </Button>
      </div>
      <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
        <DataTable
          columns={columns}
          data={trucks}
          config={{
            enableGlobalFilter: true,
            enableSorting: true,
            initialSorting: {
              id: 'unit_number',
              desc: false,
            },
            persistSort: true,
            filters: filters,
          }}
        />
      </div>
    </AppLayout>
  );
}
