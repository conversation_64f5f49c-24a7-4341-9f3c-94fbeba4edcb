import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  VisibilityState,
  ColumnFiltersState,
  SortingState,
} from '@tanstack/react-table';
import { ColumnDef } from '@/types/table';
import { useState, useCallback, useEffect, useRef } from 'react';
import { router } from '@inertiajs/react';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { PaginatedData } from '@/types/pagination';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { X, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { DataTableFacetedFilter } from './data-table-faceted-filter';
import { cn } from '@/lib/utils';

interface SearchInputProps {
  placeholder?: string;
  defaultValue?: string;
  onSearch: (value: string) => void;
  className?: string;
}

function SearchInput({ placeholder, defaultValue, onSearch, className }: SearchInputProps) {
  const [value, setValue] = useState(defaultValue || '');

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onSearch(value);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [value, onSearch]);

  return (
    <Input
      placeholder={placeholder}
      value={value}
      onChange={(event) => setValue(event.target.value)}
      className={className}
    />
  );
}

interface DataTableFilterOption {
  label: string;
  value: string;
  icon?: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface DataTableFilterConfig {
  columnId: string;
  title: string;
  type: 'faceted' | 'search' | 'range' | 'date';
  options?: DataTableFilterOption[];
  placeholder?: string;
  multiple?: boolean;
}

interface DataTableConfig {
  enableGlobalFilter?: boolean;
  enableSorting?: boolean;
  initialSorting?: {
    id: string;
    desc: boolean;
  };
  persistSort?: boolean;
  filters?: any[];
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[] | PaginatedData<TData>;
  config?: DataTableConfig;
}

function convertFilters(filters?: any[]): DataTableFilterConfig[] {
  if (!filters) return [];

  return filters.map(filter => ({
    ...filter,
    options: filter.options?.map((option: any) => ({
      label: option.label,
      value: option.value,
      count: option.count,
      icon: undefined,
    })),
  }));
}

export function DataTable<TData, TValue>({ columns, data, config }: DataTableProps<TData, TValue>) {
  const { enableSorting = false, initialSorting, persistSort = false } = config || {};
  const convertedFilters = convertFilters(config?.filters);

  const defaultVisibility: VisibilityState = {};

  columns.forEach((column) => {
    if (column.id) {
      defaultVisibility[column.id as string] =
        column.meta?.initiallyVisible !== undefined ? column.meta.initiallyVisible : true;
    }
  });

  const isFirstRender = useRef(true);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(defaultVisibility);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const filters: ColumnFiltersState = [];
    for (const [key, value] of urlParams.entries()) {
      const filterMatch = key.match(/^filter\[(.+)\]$/);
      if (filterMatch) {
        const columnId = filterMatch[1];
        if (columnId === 'global') continue;
        const values = value.split(',').filter(v => v.trim() !== '');
        if (values.length > 0) {
          filters.push({
            id: columnId,
            value: values,
          });
        }
      }
    }
    return filters;
  });

  const [sorting, setSorting] = useState<SortingState>(() => {
    if (initialSorting) {
      return [{ id: initialSorting.id, desc: initialSorting.desc }];
    }

    const urlParams = new URLSearchParams(window.location.search);
    const sortParam = urlParams.get('sort');
    if (sortParam) {
      return sortParam.split(',').map((s) => {
        if (s.startsWith('-')) {
          return { id: s.slice(1), desc: true };
        }
        return { id: s, desc: false };
      });
    }
    return [];
  });
  const [globalFilter, setGlobalFilter] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('filter[global]') || '';
  });

  const handleSortingChange = useCallback(
    (updaterOrValue: SortingState | ((old: SortingState) => SortingState)) => {
      const newSorting = typeof updaterOrValue === 'function' ? updaterOrValue(sorting) : updaterOrValue;
      setSorting(newSorting);

      if (!isFirstRender.current && persistSort && enableSorting) {
        const currentUrl = new URL(window.location.href);
        const searchParams = new URLSearchParams(currentUrl.search);

        if (newSorting.length > 0) {
          const sortParam = newSorting.map((sort) => (sort.desc ? `-${sort.id}` : sort.id)).join(',');
          searchParams.set('sort', sortParam);
        } else {
          searchParams.delete('sort');
        }

        router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
          preserveState: true,
          preserveScroll: true,
          replace: true,
        });
      }
    },
    [sorting, persistSort, enableSorting],
  );

  const handleGlobalFilterChange = useCallback((value: string) => {
    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);
    const prevValue = searchParams.get('filter[global]') || '';
    if (value && value.trim() !== '') {
      searchParams.set('filter[global]', value);
    } else {
      searchParams.delete('filter[global]');
    }
    if (prevValue !== value) {
      searchParams.delete('page');
    }
    if (!isFirstRender.current && prevValue !== value) {
      router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
      });
    }
    setGlobalFilter(value);
  }, []);

  const handleSearchFilterChange = useCallback((columnId: string, value: string) => {
    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);
    const prevValue = searchParams.get(`filter[${columnId}]`) || '';
    if (value && value.trim() !== '') {
      searchParams.set(`filter[${columnId}]`, value);
    } else {
      searchParams.delete(`filter[${columnId}]`);
    }
    if (prevValue !== value) {
      searchParams.delete('page');
    }
    if (!isFirstRender.current && prevValue !== value) {
      router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
      });
    }
  }, []);

  useCallback((columnId: string, values: string[]) => {
    const currentUrl = new URL(window.location.href);
    const searchParams = new URLSearchParams(currentUrl.search);
    const prevValue = searchParams.get(`filter[${columnId}]`) || '';
    const newValue = values.join(',');
    if (values && values.length > 0) {
      searchParams.set(`filter[${columnId}]`, newValue);
    } else {
      searchParams.delete(`filter[${columnId}]`);
    }
    if (prevValue !== newValue) {
      searchParams.delete('page');
    }
    if (!isFirstRender.current && prevValue !== newValue) {
      router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
        preserveState: true,
        preserveScroll: true,
        replace: true,
      });
    }
  }, []);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
    }
  }, []);

  const tableData = Array.isArray(data) ? data : (data as PaginatedData<TData>).data || [];
  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    onColumnVisibilityChange: setColumnVisibility,
    onColumnFiltersChange: useCallback((updaterOrValue: ColumnFiltersState | ((old: ColumnFiltersState) => ColumnFiltersState)) => {
      const newFilters = typeof updaterOrValue === 'function' ? updaterOrValue(columnFilters) : updaterOrValue;
      const filtersChanged = JSON.stringify(columnFilters.sort((a, b) => a.id.localeCompare(b.id))) !==
                            JSON.stringify(newFilters.sort((a, b) => a.id.localeCompare(b.id)));
      setColumnFilters(newFilters);
      if (!isFirstRender.current && filtersChanged) {
        const currentUrl = new URL(window.location.href);
        const searchParams = new URLSearchParams(currentUrl.search);
        for (const [key] of Array.from(searchParams.entries())) {
          if (key.match(/^filter\[.+\]$/)) {
            searchParams.delete(key);
          }
        }
        searchParams.delete('page');
        newFilters.forEach((filter) => {
          if (filter.value) {
            const values = Array.isArray(filter.value) ? filter.value : [filter.value];
            if (values.length > 0) {
              searchParams.set(`filter[${filter.id}]`, values.join(','));
            }
          }
        });
        router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
          preserveState: true,
          preserveScroll: true,
          replace: true,
        });
      }
    }, [columnFilters]),
    onSortingChange: enableSorting ? handleSortingChange : undefined,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      columnVisibility,
      columnFilters,
      sorting: enableSorting ? sorting : undefined,
      globalFilter,
    },
    initialState: {
      columnVisibility: defaultVisibility,
    },
    enableSorting,
    manualSorting: persistSort,
  });

  const isFiltered = table.getState().columnFilters.length > 0 || globalFilter.length > 0;

  return (
    <div>
      <div className="flex items-center justify-between">
        <div className="flex flex-1 items-center space-x-2">
          {config?.enableGlobalFilter !== false && (
            <Input
              placeholder="Search all columns..."
              value={globalFilter ?? ''}
              onChange={(event) => handleGlobalFilterChange(String(event.target.value))}
              className="h-8 w-[150px] lg:w-[250px]"
            />
          )}
          {convertedFilters?.map((filterConfig) => {
            const column = table.getColumn(filterConfig.columnId);
            if (!column) return null;
            if (filterConfig.type === 'faceted') {
              const options = filterConfig.options || [];
              return (
                <DataTableFacetedFilter
                  key={filterConfig.columnId}
                  column={column}
                  title={filterConfig.title}
                  options={options}
                />
              );
            }
            if (filterConfig.type === 'search') {
              const urlParams = new URLSearchParams(window.location.search);
              const initialValue = urlParams.get(`filter[${filterConfig.columnId}]`) || '';
              return (
                <SearchInput
                  key={filterConfig.columnId}
                  placeholder={filterConfig.placeholder || `Filter ${filterConfig.title}...`}
                  defaultValue={initialValue}
                  onSearch={(value) => handleSearchFilterChange(filterConfig.columnId, value)}
                  className="h-8 w-[150px] lg:w-[200px]"
                />
              );
            }
            return null;
          })}
          {isFiltered && (
            <Button
              variant="ghost"
              onClick={() => {
                table.resetColumnFilters();
                setGlobalFilter('');
                const currentUrl = new URL(window.location.href);
                const searchParams = new URLSearchParams(currentUrl.search);
                for (const [key] of Array.from(searchParams.entries())) {
                  if (key.match(/^filter\[.+\]$/)) {
                    searchParams.delete(key);
                  }
                }
                searchParams.delete('page');
                router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
                  preserveState: true,
                  preserveScroll: true,
                  replace: true,
                });
              }}
              className="h-8 px-2 lg:px-3"
            >
              Reset
              <X className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="ml-auto">
              Columns
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) => column.toggleVisibility(value)}
                  >
                    {typeof column.columnDef.header === 'string' ? column.columnDef.header : column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex items-center space-x-2 py-4">
        {table.getState().columnFilters.length > 0 && (
          <div className="flex items-center space-x-2">
            <span className="text-muted-foreground text-sm">Active filters:</span>
            {table.getState().columnFilters.map((filter) => {
              if (filter.id === 'global') return null;
              const column = table.getColumn(filter.id);
              const filterValue = Array.isArray(filter.value) ? filter.value : [filter.value];
              return filterValue?.map((value) => (
                <Badge key={`${filter.id}-${value}`} variant="secondary" className="rounded-sm">
                  {typeof column?.columnDef.header === 'string' ? column.columnDef.header : filter.id}: {value}
                  <button
                    className="ring-offset-background hover:bg-muted ml-1 transition-colors"
                    onClick={() => {
                      const newValue = filterValue.filter((v) => v !== value);
                      column?.setFilterValue(newValue.length > 0 ? newValue : undefined);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ));
            })}
          </div>
        )}
        {globalFilter && (
          <Badge variant="secondary" className="rounded-sm">
            Search: {globalFilter}
            <button
              className="ring-offset-background hover:bg-muted ml-1 transition-colors"
              onClick={() => handleGlobalFilterChange('')}
            >
              <X className="h-3 w-3" />
            </button>
          </Badge>
        )}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  const canSort = enableSorting && header.column.getCanSort();
                  const sortDirection = header.column.getIsSorted();
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : (
                        <div className={cn('flex items-center space-x-2', canSort && 'cursor-pointer select-none')}>
                          {canSort ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="data-[state=open]:bg-accent -ml-3 h-8"
                              onClick={header.column.getToggleSortingHandler()}
                            >
                              <span>{flexRender(header.column.columnDef.header, header.getContext())}</span>
                              {sortDirection === 'asc' && <ArrowUp className="ml-2 h-4 w-4" />}
                              {sortDirection === 'desc' && <ArrowDown className="ml-2 h-4 w-4" />}
                              {!sortDirection && <ArrowUpDown className="ml-2 h-4 w-4" />}
                            </Button>
                          ) : (
                            flexRender(header.column.columnDef.header, header.getContext())
                          )}
                        </div>
                      )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        {!Array.isArray(data) && (data as PaginatedData<TData>).links && (
          <>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-muted-foreground">
                Page {(data as PaginatedData<TData>).current_page || 1} of {(data as PaginatedData<TData>).last_page || 1}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentUrl = new URL(window.location.href);
                  const searchParams = new URLSearchParams(currentUrl.search);
                  const currentPage = (data as PaginatedData<TData>).current_page || 1;
                  const prevPage = currentPage - 1;
                  if (prevPage > 1) {
                    searchParams.set('page', prevPage.toString());
                  } else {
                    searchParams.set('page', '1');
                  }
                  router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
                    preserveState: true,
                    preserveScroll: true,
                  });
                }}
                disabled={(data as PaginatedData<TData>).current_page <= 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currentUrl = new URL(window.location.href);
                  const searchParams = new URLSearchParams(currentUrl.search);
                  const currentPage = (data as PaginatedData<TData>).current_page || 1;
                  const nextPage = currentPage + 1;
                  searchParams.set('page', nextPage.toString());
                  router.get(currentUrl.pathname, Object.fromEntries(searchParams), {
                    preserveState: true,
                    preserveScroll: true,
                  });
                }}
                disabled={(data as PaginatedData<TData>).current_page >= ((data as PaginatedData<TData>).last_page || 1)}
              >
                Next
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
