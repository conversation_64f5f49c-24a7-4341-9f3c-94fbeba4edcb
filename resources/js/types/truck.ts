import { FleetStatus } from './fleetStatus';
import { OwnershipType } from './ownershipType';
import { PaginatedData } from './pagination';

export interface Truck {
  id: number;
  unit_number: string;
  make?: string | null;
  model?: string | null;
  year?: number | null;
  vin?: string | null;
  status?: string | null;
  regulatory_compliance_expiry?: string | null;
  owner_company?: string | null;
  fleet_status?: FleetStatus | null;
  activation_date?: string | null;
  deactivation_date?: string | null;
  lease_termination_date?: string | null;
  rent_lease_type?: string | null;
  gps_note?: string | null;
  gateway_serial?: string | null;
  deer_guards: boolean;
  dash_camera: boolean;
  created_at?: string;
  updated_at?: string;
  has_headrack: boolean;
  ownership_type?: OwnershipType | null;
  plate?: string | null;
  plate_region?: string | null;
  driver_1_id?: number | null;
  driver_2_id?: number | null;
  plate_expiration_date?: string | null;
  deactivation_reason?: string | null;
}

export type CreateTruckDTO = Omit<Truck, 'id' | 'created_at' | 'updated_at'>;

export interface FilterOption {
  label: string;
  value: string;
  icon?: string;
  count?: number;
}

export interface FilterConfig {
  columnId: string;
  title: string;
  type: 'faceted' | 'search' | 'range' | 'date';
  options?: FilterOption[];
  placeholder?: string;
  multiple?: boolean;
}

export interface TruckPageProps {
  trucks: PaginatedData<Truck>;
  filters: FilterConfig[];
  [key: string]: unknown;
}

export interface TruckFormData {
  unit_number: string;
  make: string | null;
  model: string | null;
  year: number | null;
  vin: string | null;
  regulatory_compliance_expiry: string | null;
  owner_company: string | null;
  fleet_status: FleetStatus | null;
  activation_date: string | null;
  deactivation_date: string | null;
  lease_termination_date: string | null;
  gps_note: string | null;
  gateway_serial: string | null;
  deer_guards: boolean;
  dash_camera: boolean;
  has_headrack: boolean;
  ownership_type: OwnershipType | null;
  plate: string | null;
  plate_region: string | null;
  driver_1_id: number | null;
  driver_2_id: number | null;
  plate_expiration_date: string | null;
  deactivation_reason: string | null;
  status: string | null;
}
